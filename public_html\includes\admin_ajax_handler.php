<?php
ob_start();
ini_set('display_errors', 0);
error_reporting(E_ALL);

function ajax_error_handler($errno, $errstr, $errfile, $errline) {
    $error_message = "PHP Error [$errno]: $errstr in $errfile on line $errline";

    while (ob_get_level() > 0) {
        ob_end_clean();
    }

    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Ocorreu um erro no servidor. Por favor, tente novamente.',
        'debug' => DEBUG_MODE ? $error_message : null
    ]);
    exit;
}

set_error_handler('ajax_error_handler', E_ALL & ~E_NOTICE & ~E_WARNING);

try {
    require_once __DIR__ . '/../config.php';
    require_once __DIR__ . '/db.php';
    require_once __DIR__ . '/functions.php';
    require_once __DIR__ . '/security.php';
    require_once __DIR__ . '/order_functions.php';
    require_once __DIR__ . '/order_statuses.php';

    require_once __DIR__ . '/session.php';
    $current_session_id = start_cookieless_session();

    function send_json_response($success, $message = '', $data = []) {
        while (ob_get_level() > 0) {
            ob_end_clean();
        }

        $response = [
            'success' => $success,
            'message' => $message
        ];

        if (!empty($data)) {
            $response['data'] = $data;
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    $is_admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

    if (!$is_admin_logged_in) {
        send_json_response(false, 'Não tem permissão para aceder a esta área.');
    }

    $submitted_csrf = $_POST['csrf_token'] ?? '';

    if (!validate_csrf_token($submitted_csrf)) {
        send_json_response(false, 'Erro de segurança (CSRF). Tente novamente.');
    } else {
    }

$section = $_POST['section'] ?? '';
$action = $_POST['action'] ?? '';
$item_id = isset($_POST['id']) ? (int)$_POST['id'] : null;

if ($action === 'get_file_type_associations' && isset($_POST['file_id'])) {
    
    if (ob_get_level()) ob_end_clean();

    
    header('Content-Type: application/json');

    require_once __DIR__ . '/digital_files_functions.php';

    $file_id = (int)$_POST['file_id'];
    if ($file_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID de arquivo inválido.']);
        exit;
    }

    try {
        
        $file_type_ids = get_digital_file_file_type_ids($file_id);

        
        echo json_encode([
            'success' => true,
            'message' => 'Tipos de arquivo obtidos com sucesso.',
            'file_type_ids' => $file_type_ids
        ]);
        exit;
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao obter tipos de arquivo: ' . $e->getMessage()
        ]);
        exit;
    }
}

if ($action === 'update_file_types' && isset($_POST['file_id'])) {
    require_once __DIR__ . '/digital_files_functions.php';

    
    $submitted_csrf = $_POST['csrf_token'] ?? '';

    
    if (!validate_csrf_token($submitted_csrf)) {
        
        
        
    }

    $file_id = (int)$_POST['file_id'];
    if ($file_id <= 0) {
        send_json_response(false, 'ID de arquivo inválido.');
        exit;
    }

    
    $file = get_digital_file_by_id($file_id);
    if (!$file) {
        send_json_response(false, 'Arquivo digital não encontrado.');
        exit;
    }

    $file_type_ids = isset($_POST['file_types']) && is_array($_POST['file_types'])
        ? array_map('intval', $_POST['file_types'])
        : [];

    try {
        $result = update_digital_file_file_types($file_id, $file_type_ids);

        if ($result) {

            
            $updated_file_types_data = get_digital_file_file_types($file_id);

            $updated_types_html = '';
            if (!empty($updated_file_types_data)) {
                foreach ($updated_file_types_data as $type) {
                    $updated_types_html .= '<span class="badge bg-info text-dark me-1">' . sanitize_input($type['extension']) . '</span>';
                }
            } else {
                $updated_types_html = '<span class="text-muted">Nenhum tipo definido</span>';
            }

            send_json_response(true, 'Tipos de arquivo atualizados com sucesso.', [
                'updated_types_html' => $updated_types_html,
                'file_type_ids' => array_map(function($type) { return (int)$type['id']; }, $updated_file_types_data)
            ]);
        } else {
            send_json_response(false, 'Erro ao atualizar tipos de arquivo.');
        }
    } catch (Exception $e) {
        send_json_response(false, 'Erro ao atualizar tipos de arquivo: ' . $e->getMessage());
    }
}

if (empty($section) && isset($_GET['section'])) {
    $section = $_GET['section'];
}

if (empty($item_id) && isset($_GET['id'])) {
    $item_id = (int)$_GET['id'];
}

if (empty($section)) {
    $section = 'orders';
}

if (empty($item_id) && $section === 'orders' && isset($_SERVER['HTTP_REFERER'])) {
    $referer_url = parse_url($_SERVER['HTTP_REFERER']);
    if (isset($referer_url['query'])) {
        parse_str($referer_url['query'], $query_params);
        if (isset($query_params['id'])) {
            $item_id = (int)$query_params['id'];
        }
    }
}

switch ($section) {
    case 'orders':

        if (isset($_POST['update_status'])) {

            $new_status = sanitize_input($_POST['order_status'] ?? '');
            $tracking_number = sanitize_input($_POST['tracking_number'] ?? '');
            $tracking_url = sanitize_input($_POST['tracking_url'] ?? '');
            $admin_notes = sanitize_input($_POST['admin_notes'] ?? '');
            $notify_customer = isset($_POST['notify_customer']) && $_POST['notify_customer'] == '1';

            try {

                $pdo = get_db_connection();
                $notes_sql = "UPDATE orders SET admin_notes = :admin_notes WHERE id = :id";
                $notes_stmt = $pdo->prepare($notes_sql);
                $notes_stmt->execute([
                    ':id' => $item_id,
                    ':admin_notes' => $admin_notes
                ]);

                $result = update_order_status($item_id, $new_status, $notify_customer, $tracking_number, $tracking_url);

                if ($result) {
                    $notification_msg = $notify_customer ? ' Cliente notificado por email.' : '';
                    send_json_response(true, 'Estado da encomenda atualizado com sucesso!' . $notification_msg);
                } else {
                    send_json_response(true, 'Nenhuma alteração foi feita no estado da encomenda.');
                }
            } catch (Exception $e) {
                send_json_response(false, 'Erro ao atualizar o estado da encomenda.');
            }
        }

        if (isset($_POST['contact_client'])) {
            $client_email = sanitize_input($_POST['client_email'] ?? '');
            $client_name = sanitize_input($_POST['client_name'] ?? '');
            $order_ref = sanitize_input($_POST['order_ref'] ?? '');
            $subject = sanitize_input($_POST['message_subject'] ?? '');
            $message = sanitize_input($_POST['message_body'] ?? '');

            if (empty($client_email) || empty($subject) || empty($message)) {
                send_json_response(false, 'Todos os campos são obrigatórios.');
            }

            try {

                $pdo = get_db_connection();
                $sql = "INSERT INTO contacts (name, email, subject, message, order_id, is_from_admin, created_at)
                        VALUES (:name, :email, :subject, :message, :order_id, 1, NOW())";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':name' => $client_name,
                    ':email' => $client_email,
                    ':subject' => $subject,
                    ':message' => $message,
                    ':order_id' => $item_id
                ]);

                $store_name = get_setting('store_name');
                $store_email = get_setting('store_email');

                $email_subject = "[{$store_name}] {$subject}";
                $email_body = $message;
                $email_body .= "\n\n--\n";
                $email_body .= "Esta mensagem é referente à sua encomenda #{$order_ref}.\n";
                $email_body .= "Por favor não responda diretamente a este email. Utilize o formulário de contacto no nosso site.";

                $headers = "From: {$store_name} <{$store_email}>\r\n";
                $headers .= "Reply-To: {$store_email}\r\n";

                if (mail($client_email, $email_subject, $email_body, $headers)) {
                    send_json_response(true, 'Mensagem enviada com sucesso!');
                } else {
                    send_json_response(false, 'Erro ao enviar email, mas a mensagem foi guardada.');
                }
            } catch (Exception $e) {
                send_json_response(false, 'Erro ao enviar mensagem.');
            }
        }

        if (isset($_POST['anonymize_order'])) {
            $notify_customer = isset($_POST['notify_anonymization']) && $_POST['notify_anonymization'] == '1';

            if (anonymize_order($item_id, $notify_customer)) {
                $notification_msg = $notify_customer ? ' Cliente notificado por email.' : '';
                send_json_response(true, 'Dados do cliente anonimizados com sucesso.' . $notification_msg);
            } else {
                send_json_response(false, 'Erro ao anonimizar os dados do cliente.');
            }
        }

        if (isset($_POST['delete_order'])) {

            if (empty($item_id)) {
                send_json_response(false, 'ID da encomenda em falta.');
            }

            try {
                $result = delete_order($item_id);

                if ($result) {
                    send_json_response(true, 'Encomenda eliminada com sucesso.');
                } else {
                    send_json_response(false, 'Erro ao eliminar a encomenda.');
                }
            } catch (Exception $e) {
                send_json_response(false, 'Erro ao eliminar a encomenda: ' . $e->getMessage());
            }
        }

        if (isset($_POST['update_customer_info'])) {

            $vat_id = sanitize_input($_POST['customer_vat_id'] ?? '');

            $vat_id = preg_replace('/\D/', '', $vat_id);

            $formatted_vat_id = !empty($vat_id) ? 'PT' . $vat_id : '';

            $customer_data = [
                'customer_name' => sanitize_input($_POST['customer_name'] ?? ''),
                'customer_email' => sanitize_input($_POST['customer_email'] ?? ''),
                'customer_phone' => sanitize_input($_POST['customer_phone'] ?? ''),
                'customer_vat_id' => $formatted_vat_id,
                'shipping_address' => sanitize_input($_POST['shipping_address'] ?? ''),
                'shipping_city' => sanitize_input($_POST['shipping_city'] ?? ''),
                'shipping_zip' => sanitize_input($_POST['shipping_zip'] ?? ''),
                'shipping_country' => sanitize_input($_POST['shipping_country'] ?? ''),
                'order_notes' => sanitize_input($_POST['order_notes'] ?? '')
            ];

            if (isset($_POST['billing_name']) && !empty($_POST['billing_name'])) {
                $customer_data['billing_name'] = sanitize_input($_POST['billing_name']);
                $customer_data['billing_nif'] = sanitize_input($_POST['billing_nif'] ?? '');
                $customer_data['billing_address'] = sanitize_input($_POST['billing_address'] ?? '');
                $customer_data['billing_city'] = sanitize_input($_POST['billing_city'] ?? '');
                $customer_data['billing_zip'] = sanitize_input($_POST['billing_zip'] ?? '');
                $customer_data['billing_country'] = sanitize_input($_POST['billing_country'] ?? '');
            }

            if (update_order_customer_info($item_id, $customer_data)) {
                send_json_response(true, 'Dados do cliente atualizados com sucesso.');
            } else {
                send_json_response(false, 'Erro ao atualizar os dados do cliente.');
            }
        }

        if (isset($_POST['update_payment_method'])) {
            $payment_method = sanitize_input($_POST['payment_method'] ?? '');

            try {
                $result = update_order_payment_method($item_id, $payment_method);
                if ($result) {
                    send_json_response(true, 'Método de pagamento atualizado com sucesso.');
                } else {
                    send_json_response(false, 'Erro ao atualizar o método de pagamento.');
                }
            } catch (Exception $e) {
                send_json_response(false, 'Erro ao atualizar o método de pagamento: ' . $e->getMessage());
            }
        }

        if (isset($_POST['resend_order_details'])) {
            if (resend_order_details($item_id)) {
                send_json_response(true, 'Detalhes da encomenda reenviados com sucesso.');
            } else {
                send_json_response(false, 'Erro ao reenviar os detalhes da encomenda.');
            }
        }

        if (isset($_POST['reply_to_message'])) {
            $message_id = (int)($_POST['message_id'] ?? 0);
            $reply_message = sanitize_input($_POST['reply_message'] ?? '');

            if (empty($reply_message) || $message_id <= 0) {
                send_json_response(false, 'Mensagem de resposta é obrigatória.');
            }

            try {

                $original_message = db_query(
                    "SELECT * FROM contacts WHERE id = :id",
                    [':id' => $message_id],
                    true
                );

                if (!$original_message) {
                    send_json_response(false, 'Mensagem original não encontrada.');
                }

                $pdo = get_db_connection();
                $sql = "INSERT INTO message_replies (message_id, reply_body, sent_at)
                        VALUES (:message_id, :reply_body, datetime('now', 'localtime'))";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':message_id' => $message_id,
                    ':reply_body' => $reply_message
                ]);

                $update_sql = "UPDATE contacts SET status = 'replied' WHERE id = :id";
                $update_stmt = $pdo->prepare($update_sql);
                $update_stmt->execute([':id' => $message_id]);

                $client_email = $original_message['email'];
                $client_name = $original_message['name'];
                $original_subject = $original_message['subject'];

                $store_name = get_setting('store_name');
                $store_email = get_setting('store_email');

                $email_subject = "Re: [{$store_name}] {$original_subject}";
                $email_body = "Olá {$client_name},\n\n";
                $email_body .= $reply_message;
                $email_body .= "\n\n--\n";
                $email_body .= "Esta é uma resposta à sua mensagem: \"{$original_subject}\".\n";
                $email_body .= "Por favor não responda diretamente a este email. Utilize o formulário de contacto no nosso site.";

                $headers = "From: {$store_name} <{$store_email}>\r\n";
                $headers .= "Reply-To: {$store_email}\r\n";

                if (mail($client_email, $email_subject, $email_body, $headers)) {
                    send_json_response(true, 'Resposta enviada com sucesso!');
                } else {
                    send_json_response(false, 'Erro ao enviar email, mas a resposta foi guardada.');
                }
            } catch (Exception $e) {
                send_json_response(false, 'Erro ao enviar resposta.');
            }
        }
        break;

    case 'vat_rates':
        require_once __DIR__ . '/vat_functions.php';

        if ($action === 'create_vat') {

            $data = [
                'rate' => $_POST['rate'] ?? null,
                'description' => $_POST['description'] ?? null,
                'is_default' => isset($_POST['is_default']) && $_POST['is_default'] == '1'
            ];

            $result = create_vat_rate($data);

            send_json_response(
                $result['success'],
                $result['message'],
                $result['success'] ? ['vat_rate' => $result['vat_rate']] : []
            );
        }

        if ($action === 'update_vat') {
             if (!$item_id) {
                send_json_response(false, 'ID da taxa de IVA em falta.');
            }

             $data = [
                'rate' => $_POST['rate'] ?? null,
                'description' => $_POST['description'] ?? null,
                'is_default' => isset($_POST['is_default']) && $_POST['is_default'] == '1'
            ];

            $result = update_vat_rate($item_id, $data);

            send_json_response(
                $result['success'],
                $result['message'],
                 $result['success'] ? ['vat_rate' => $result['vat_rate']] : []
            );
        }

        if ($action === 'set_default_vat') {
            if (!$item_id) {
                send_json_response(false, 'ID da taxa de IVA em falta.');
            }

            $result = set_default_vat_rate($item_id);

            send_json_response(
                $result['success'],
                $result['message']

            );
        }

        if ($action === 'delete_vat') {
             if (!$item_id) {
                send_json_response(false, 'ID da taxa de IVA em falta.');
            }

            $result = delete_vat_rate($item_id);

            send_json_response(
                $result['success'],
                $result['message']

            );
        }
        break;

    case 'products':
        require_once __DIR__ . '/attribute_functions.php';

        $attribute_id = isset($_GET['attribute_id']) ? (int)$_GET['attribute_id'] : null;
        $product_id = isset($_GET['product_id']) ? (int)$_GET['product_id'] : null;

        $is_get_request = $_SERVER['REQUEST_METHOD'] === 'GET';
        if (!$is_get_request) {

            $submitted_csrf = $_POST['csrf_token'] ?? '';
             if (!validate_csrf_token($submitted_csrf)) {
                 send_json_response(false, 'Erro de segurança (CSRF). Tente novamente.');
             }
        }

        if ($action === 'get_attribute_values') {

            if (!$attribute_id) {
                send_json_response(false, 'ID do atributo em falta.');
            }

            try {

                $values = get_attribute_values($attribute_id);

                if (count($values) > 0) {
                }

                while (ob_get_level() > 0) { ob_end_clean(); }
                header('Content-Type: application/json');

                $response = [
                    'success' => true,
                    'values' => $values
                ];

                $json_result = json_encode($response);
                if ($json_result === false) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Erro ao codificar valores: ' . json_last_error_msg()
                    ]);
                } else {
                    echo $json_result;
                }
                exit;

            } catch (Exception $e) {
                send_json_response(false, 'Erro ao buscar valores do atributo: ' . $e->getMessage());
            }
        } elseif ($action === 'update_image_order') {

            $product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
            $image_order = isset($_POST['image_order']) ? json_decode($_POST['image_order'], true) : [];

            if (!$product_id || empty($image_order)) {
                send_json_response(false, 'Dados inválidos para atualizar a ordem das imagens.');
            }

            try {
                $pdo = get_db_connection();
                $pdo->beginTransaction();

                $update_sql = "UPDATE product_images SET sort_order = :sort_order WHERE id = :id AND product_id = :product_id";
                $update_stmt = $pdo->prepare($update_sql);

                foreach ($image_order as $index => $image_id) {
                    $update_stmt->execute([
                        ':sort_order' => $index,
                        ':id' => (int)$image_id,
                        ':product_id' => $product_id
                    ]);
                }

                $pdo->commit();
                send_json_response(true, 'Ordem das imagens atualizada com sucesso.');

            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                send_json_response(false, 'Erro ao atualizar a ordem das imagens: ' . $e->getMessage());
            }
        } else {

             send_json_response(false, "Ação de produto não suportada: $action");
        }
        break;

    case 'custom_fields':
        require_once __DIR__ . '/custom_field_functions.php';

        if ($action === 'create') {

            $field_data = [
                'name' => $_POST['name'] ?? '',
                'description' => $_POST['description'] ?? '',
                'field_type_id' => isset($_POST['field_type_id']) ? (int)$_POST['field_type_id'] : 0,
                'min_chars' => isset($_POST['min_chars']) ? (int)$_POST['min_chars'] : 0,
                'max_chars' => isset($_POST['max_chars']) ? (int)$_POST['max_chars'] : 255,
                'price_modifier' => isset($_POST['price_modifier']) ? (float)$_POST['price_modifier'] : 0.0,
                'is_required' => isset($_POST['is_required']) ? (int)$_POST['is_required'] : 0,
                'is_active' => isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1
            ];

            if (isset($_POST['config']) && is_array($_POST['config'])) {
                $field_data['config'] = $_POST['config'];
            }

            if (empty($field_data['name']) || empty($field_data['field_type_id'])) {
                send_json_response(false, 'Nome e tipo de campo são obrigatórios.');
            }

            $field_id = create_custom_field($field_data);

            if ($field_id) {
                send_json_response(true, 'Campo personalizado criado com sucesso.', ['field_id' => $field_id]);
            } else {
                send_json_response(false, 'Erro ao criar campo personalizado.');
            }
        }

        if ($action === 'update') {
            if (!$item_id) {
                send_json_response(false, 'ID do campo personalizado em falta.');
            }

            $field_data = [
                'name' => $_POST['name'] ?? '',
                'description' => $_POST['description'] ?? '',
                'field_type_id' => isset($_POST['field_type_id']) ? (int)$_POST['field_type_id'] : 0,
                'min_chars' => isset($_POST['min_chars']) ? (int)$_POST['min_chars'] : 0,
                'max_chars' => isset($_POST['max_chars']) ? (int)$_POST['max_chars'] : 255,
                'price_modifier' => isset($_POST['price_modifier']) ? (float)$_POST['price_modifier'] : 0.0,
                'is_required' => isset($_POST['is_required']) ? (int)$_POST['is_required'] : 0,
                'is_active' => isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1
            ];

            if (isset($_POST['config']) && is_array($_POST['config'])) {
                $field_data['config'] = $_POST['config'];
            }

            if (empty($field_data['name']) || empty($field_data['field_type_id'])) {
                send_json_response(false, 'Nome e tipo de campo são obrigatórios.');
            }

            $result = update_custom_field($item_id, $field_data);

            if ($result) {
                send_json_response(true, 'Campo personalizado atualizado com sucesso.');
            } else {
                send_json_response(false, 'Erro ao atualizar campo personalizado.');
            }
        }
        break;

    case 'custom_field_fonts':
        require_once __DIR__ . '/custom_field_functions.php';

        if ($action === 'create') {

            $font_data = [
                'name' => $_POST['name'] ?? '',
                'is_active' => isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1
            ];

            $font_type = $_POST['font_type'] ?? '';

            if ($font_type === 'google') {
                $font_data['google_font_name'] = $_POST['google_font_name'] ?? '';

                if (empty($font_data['google_font_name'])) {
                    send_json_response(false, 'Nome da fonte do Google é obrigatório.');
                }
            } elseif ($font_type === 'file') {

                if (isset($_FILES['font_file']) && $_FILES['font_file']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = PROJECT_ROOT . '/public/uploads/fonts/';

                    if (!file_exists($upload_dir)) {
                        if (!mkdir($upload_dir, 0755, true)) {
                            send_json_response(false, 'Erro ao criar diretório para fontes.');
                        }
                    }

                    $file_info = pathinfo($_FILES['font_file']['name']);
                    $extension = strtolower($file_info['extension']);
                    $safe_name = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', $file_info['filename']);
                    $unique_filename = 'font_' . time() . '_' . $safe_name . '.' . $extension;
                    $destination = $upload_dir . $unique_filename;

                    if (move_uploaded_file($_FILES['font_file']['tmp_name'], $destination)) {
                        $font_data['file_path'] = '/public/uploads/fonts/' . $unique_filename;
                    } else {
                        send_json_response(false, 'Erro ao mover o ficheiro de fonte.');
                    }
                } else {
                    send_json_response(false, 'Ficheiro de fonte é obrigatório.');
                }
            } else {
                send_json_response(false, 'Tipo de fonte inválido.');
            }

            if (empty($font_data['name'])) {
                send_json_response(false, 'Nome da fonte é obrigatório.');
            }

            $font_id = create_custom_field_font($font_data);

            if ($font_id) {
                send_json_response(true, 'Fonte adicionada com sucesso.', ['font_id' => $font_id]);
            } else {
                send_json_response(false, 'Erro ao adicionar fonte.');
            }
        }

        if ($action === 'update') {
            if (!$item_id) {
                send_json_response(false, 'ID da fonte em falta.');
            }

            $font_data = [
                'name' => $_POST['name'] ?? '',
                'is_active' => isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1
            ];

            $font_type = $_POST['font_type'] ?? '';

            if ($font_type === 'google') {
                $font_data['google_font_name'] = $_POST['google_font_name'] ?? '';
                $font_data['file_path'] = null;

                if (empty($font_data['google_font_name'])) {
                    send_json_response(false, 'Nome da fonte do Google é obrigatório.');
                }
            } elseif ($font_type === 'file') {

                if (isset($_FILES['font_file']) && $_FILES['font_file']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = PROJECT_ROOT . '/public/uploads/fonts/';

                    if (!file_exists($upload_dir)) {
                        if (!mkdir($upload_dir, 0755, true)) {
                            send_json_response(false, 'Erro ao criar diretório para fontes.');
                        }
                    }

                    $file_info = pathinfo($_FILES['font_file']['name']);
                    $extension = strtolower($file_info['extension']);
                    $safe_name = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', $file_info['filename']);
                    $unique_filename = 'font_' . time() . '_' . $safe_name . '.' . $extension;
                    $destination = $upload_dir . $unique_filename;

                    if (move_uploaded_file($_FILES['font_file']['tmp_name'], $destination)) {
                        $font_data['file_path'] = '/public/uploads/fonts/' . $unique_filename;
                        $font_data['google_font_name'] = null;
                    } else {
                        send_json_response(false, 'Erro ao mover o ficheiro de fonte.');
                    }
                } else {

                    $existing_font = get_custom_field_font($item_id);
                    if ($existing_font && !empty($existing_font['file_path'])) {
                        $font_data['file_path'] = $existing_font['file_path'];
                        $font_data['google_font_name'] = null;
                    } else {
                        send_json_response(false, 'Ficheiro de fonte é obrigatório.');
                    }
                }
            } else {
                send_json_response(false, 'Tipo de fonte inválido.');
            }

            if (empty($font_data['name'])) {
                send_json_response(false, 'Nome da fonte é obrigatório.');
            }

            $result = update_custom_field_font($item_id, $font_data);

            if ($result) {
                send_json_response(true, 'Fonte atualizada com sucesso.');
            } else {
                send_json_response(false, 'Erro ao atualizar fonte.');
            }
        }
        break;

    case 'placeholder_links':

        if ($action === 'new' || $action === 'edit') {

            $title = trim($_POST['title'] ?? '');
            $url = trim($_POST['url'] ?? '');
            $placeholder_id = (int)($_POST['placeholder_id'] ?? 0);
            $target = trim($_POST['target'] ?? '_blank');

            if ($target !== '_blank' && $target !== '_self') {
                $target = '_blank';
            }

            $errors = [];
            if (empty($title)) $errors[] = "Título do link é obrigatório.";
            if (empty($url)) $errors[] = "URL do link é obrigatório.";
            if (!filter_var($url, FILTER_VALIDATE_URL)) $errors[] = "URL inválido. Por favor, insira um URL completo (ex: https://www.example.com).";
            if ($placeholder_id <= 0) $errors[] = "Placeholder é obrigatório.";

            if (!empty($errors)) {
                $error_message = implode('<br>', array_map('sanitize_input', $errors));
                send_json_response(false, 'Por favor, corrija os seguintes erros:', ['errors' => $errors]);
                exit;
            }

            try {
                if ($action === 'new') {
                    $new_id = create_placeholder_link($title, $url, $placeholder_id, $target);
                    if ($new_id) {
                        send_json_response(true, 'Link criado com sucesso!');
                    } else {
                        throw new Exception("Falha ao criar link.");
                    }
                } else {
                    if (!$item_id) {
                        send_json_response(false, 'ID do link em falta.');
                    }

                    $updated = update_placeholder_link($item_id, $title, $url, $placeholder_id, $target);
                    if ($updated) {
                        send_json_response(true, 'Link atualizado com sucesso!');
                    } else {
                        if (!get_placeholder_link_by_id($item_id)) {
                            throw new Exception("Link não encontrado para atualização (ID: $item_id).");
                        } else {
                            throw new Exception("Falha ao atualizar link (verifique se houve alterações ou erro na base de dados).");
                        }
                    }
                }
            } catch (Exception $e) {
                send_json_response(false, 'Erro ao guardar link: ' . sanitize_input($e->getMessage()));
            }
        } else {
            send_json_response(false, "Ação não suportada para placeholder_links: $action");
        }
        break;

    case 'sitemaps':
        require_once __DIR__ . '/sitemap_functions.php';
        $sitemap_id = isset($_POST['id']) ? (int)$_POST['id'] : null;

        if ($action === 'generate_sitemap' && $sitemap_id) {
            $result = generate_sitemap($sitemap_id);
            send_json_response($result['success'], $result['message']);
        } elseif ($action === 'generate_all_sitemaps') {
            $result = generate_all_active_sitemaps();
            send_json_response($result['success'], $result['message'], ['details' => $result['details'] ?? []]);
        } elseif ($action === 'get_sitemap_details' && $sitemap_id) {
            $sitemap_data = get_sitemap_config($sitemap_id);
            if ($sitemap_data) {
                send_json_response(true, 'Detalhes do sitemap obtidos.', ['sitemapData' => $sitemap_data]);
            } else {
                send_json_response(false, 'Configuração de sitemap não encontrada.');
            }
        } else {
            send_json_response(false, "Ação de sitemap não suportada: $action");
        }
        break;

    default:
        send_json_response(false, "Ação não suportada. Secção: $section");
        break;
}

} catch (Exception $e) {

    while (ob_get_level() > 0) {
        ob_end_clean();
    }

    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Ocorreu um erro no servidor. Por favor, tente novamente.',
        'debug' => DEBUG_MODE ? $e->getMessage() : null
    ]);
    exit;
}

while (ob_get_level() > 0) {
    ob_end_clean();
}

header('Content-Type: application/json');
echo json_encode([
    'success' => false,
    'message' => 'Ocorreu um erro desconhecido. Por favor, tente novamente.'
]);
exit;
